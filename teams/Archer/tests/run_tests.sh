#!/bin/bash

# commit-msg hook 测试脚本
# 用法: ./run_tests.sh
#
# 测试规则说明:
# 1. 所有提交都必须包含 [自测内容] 标签
# 2. Fix类型提交还需要包含: [问题描述] [问题原因] [解决方案] [自测内容]
# 3. Bug提交验证规则:
#    - 标题包含"Bug"或"bug"时，必须包含"Bug 12345"格式的ID（5-11位数字）
#    - 必须包含 [bug] 标签
# 4. Head第一行必须以 [模块名] 开头，长度不超过65字符
# 5. Head与Body之间必须有空行
# 6. Body每行长度不超过70字符
# 7. 支持多模块标签格式: [模块1][模块2] 描述
#
# Git状态测试支持:
# 测试文件可以包含git状态标识来模拟不同的git操作状态
# 文件命名格式: XXX-description.state.accept/deny.txt
# 支持的状态:
#   - cherry-pick: 模拟cherry-pick操作状态
#   - rebase: 模拟rebase操作状态
#   - merge: 模拟merge操作状态
#   - revert: 模拟revert操作状态
#   - bisect: 模拟bisect操作状态
#   - worktree: 模拟worktree环境
# 例如: 025-test.cherry-pick.accept.txt 会在cherry-pick状态下测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEAM_DIR="$(dirname "$SCRIPT_DIR")"
COMMIT_MSG_HOOK="$TEAM_DIR/commit-msg"

echo "========================================"
echo "commit-msg Hook 测试套件"
echo "========================================"
echo "测试目录: $SCRIPT_DIR"
echo "Hook脚本: $COMMIT_MSG_HOOK"
echo ""

# 检查commit-msg脚本是否存在
if [ ! -f "$COMMIT_MSG_HOOK" ]; then
    echo -e "${RED}错误: commit-msg hook 不存在: $COMMIT_MSG_HOOK${NC}"
    exit 1
fi

# 确保commit-msg脚本可执行
chmod +x "$COMMIT_MSG_HOOK"

# 创建临时目录用于测试
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# 初始化临时git仓库
cd "$TEMP_DIR"
git init --quiet
git config user.name "Test User"
git config user.email "<EMAIL>"

# 创建一个初始提交
echo "test" > test.txt
git add test.txt
git commit -m "Initial commit" --quiet

echo "开始运行测试..."
echo ""

setup_git_state() {
    local state="$1"
    local git_dir="${TEMP_DIR}/.git"
    
    # 确保.git目录存在
    mkdir -p "$git_dir"
    
    # 清理所有可能的状态文件
    rm -f "$git_dir"/{CHERRY_PICK_HEAD,MERGE_HEAD,REVERT_HEAD,BISECT_LOG,BISECT_START,REBASE_HEAD}
    rm -rf "$git_dir"/{rebase-merge,rebase-apply}
    rm -f "$git_dir/commondir"

    case "$state" in
        "cherry-pick")
            echo "dummy_commit_hash" > "$git_dir/CHERRY_PICK_HEAD"
            ;;
        "rebase")
            mkdir -p "$git_dir/rebase-merge"
            echo "refs/heads/dummy" > "$git_dir/rebase-merge/head-name"
            echo "dummy_commit" > "$git_dir/rebase-merge/onto"
            ;;
        "interactive-rebase")
            mkdir -p "$git_dir/rebase-apply"
            echo "dummy" > "$git_dir/rebase-apply/head-name"
            ;;
        "merge")
            echo "dummy_commit_hash" > "$git_dir/MERGE_HEAD"
            echo "Merge message" > "$git_dir/MERGE_MSG"
            ;;
        "revert")
            echo "dummy_commit_hash" > "$git_dir/REVERT_HEAD"
            ;;
        "bisect")
            echo "git bisect start" > "$git_dir/BISECT_LOG"
            echo "dummy_commit" > "$git_dir/BISECT_START"
            ;;
        "worktree")
            mkdir -p "$git_dir/worktrees/test"
            echo "gitdir: $TEMP_DIR/.git/worktrees/test" > "$git_dir/commondir"
            ;;
    esac
}

# 解析测试文件名中的git状态
parse_git_state() {
    local test_file="$1"
    local filename=$(basename "$test_file" .txt)

    # 检查文件名中是否包含git状态标识
    # 格式: XXX-description.state.accept/deny.txt
    # 例如: 025-cherry-pick-test.cherry-pick.accept.txt
    if [[ "$filename" =~ \.(cherry-pick|rebase|merge|revert|bisect|worktree)\.(accept|deny)$ ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo "normal"
    fi
}

# 运行测试函数
run_test() {
    local test_file="$1"
    local test_name=$(basename "$test_file" .txt)
    local expected_result=""
    local git_state=""

    # 解析git状态
    git_state=$(parse_git_state "$test_file")

    # 根据文件名判断期望结果
    if [[ "$test_file" == *.accept.txt ]]; then
        expected_result="accept"
    elif [[ "$test_file" == *.deny.txt ]]; then
        expected_result="deny"
    else
        echo -e "${YELLOW}跳过: $test_name (未知测试类型)${NC}"
        return
    fi

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    # 设置git状态
    setup_git_state "$git_state"

    # 显示当前测试的git状态
    if [ "$git_state" != "normal" ]; then
        echo -n -e "${YELLOW}[${git_state}]${NC} "
    fi

    # 复制测试消息到临时文件
    local temp_msg="$TEMP_DIR/commit_msg_test"
    cp "$test_file" "$temp_msg"

    # 运行commit-msg hook
    local exit_code=0
    local hook_output=""
    hook_output=$("$COMMIT_MSG_HOOK" "$temp_msg" 2>&1) || exit_code=$?

    # 检查结果
    if [ "$expected_result" = "accept" ]; then
        if [ $exit_code -eq 0 ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望通过但被拒绝)"
            if [ -n "$hook_output" ]; then
                echo -e "${RED}    错误信息: $hook_output${NC}"
            fi
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else # deny
        if [ $exit_code -ne 0 ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望拒绝但通过了)"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi

    # 恢复正常git状态
    setup_git_state "normal"
}

# 运行所有测试文件
for test_file in "$SCRIPT_DIR"/*.txt; do
    if [ -f "$test_file" ]; then
        run_test "$test_file"
    fi
done

echo ""
echo "========================================"
echo "测试结果汇总"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}所有测试通过! ✓${NC}"
    exit 0
else
    echo -e "${RED}有 $FAILED_TESTS 个测试失败! ✗${NC}"
    exit 1
fi
