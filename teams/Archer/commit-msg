#!/usr/bin/env bash
# From Gerrit Code Review 2.12.5
#
# Part of Gerrit Code Review (https://www.gerritcodereview.com/)
#
# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

unset GREP_OPTIONS

CHANGE_ID_AFTER="Bug|Issue|Test"
MSG="$1"

# Check for, and add if missing, a unique Change-Id
#
add_ChangeId() {
        clean_message=`sed -e '
                /^diff --git .*/{
                        s///
                        q
                }
                /^Signed-off-by:/d
                /^#/d
        ' "$MSG" | git stripspace`
        if test -z "$clean_message"
        then
                exit 0
        fi

        # Do not add Change-Id to temp commits
        if echo "$clean_message" | head -1 | grep -q '^\(fixup\|squash\)!'
        then
                exit 0
        fi

        if test "false" = "`git config --bool --get gerrit.createChangeId`"
        then
                return
        fi

        # Does Change-Id: already exist? if so, exit (no change).
        if grep -i '^Change-Id:' "$MSG" >/dev/null
        then
                return
        fi

        id=`_gen_ChangeId`
        T="$MSG.tmp.$$"
        AWK=awk
        if [ -x /usr/xpg4/bin/awk ]; then
                # Solaris AWK is just too broken
                AWK=/usr/xpg4/bin/awk
        fi

        # Get core.commentChar from git config or use default symbol
        commentChar=`git config --get core.commentChar`
        commentChar=${commentChar:-#}

        # How this works:
        # - parse the commit message as (textLine+ blankLine*)*
        # - assume textLine+ to be a footer until proven otherwise
        # - exception: the first block is not footer (as it is the title)
        # - read textLine+ into a variable
        # - then count blankLines
        # - once the next textLine appears, print textLine+ blankLine* as these
        #   aren't footer
        # - in END, the last textLine+ block is available for footer parsing
        $AWK '
        BEGIN {
                # while we start with the assumption that textLine+
                # is a footer, the first block is not.
                isFooter = 0
                footerComment = 0
                blankLines = 0
        }

        # Skip lines starting with commentChar without any spaces before it.
        /^'"$commentChar"'/ { next }

        # Skip the line starting with the diff command and everything after it,
        # up to the end of the file, assuming it is only patch data.
        # If more than one line before the diff was empty, strip all but one.
        /^diff --git / {
                blankLines = 0
                while (getline) { }
                next
        }

        # Count blank lines outside footer comments
        /^$/ && (footerComment == 0) {
                blankLines++
                next
        }

        # Catch footer comment
        /^\[[a-zA-Z0-9-]+:/ && (isFooter == 1) {
                footerComment = 1
        }

        /]$/ && (footerComment == 1) {
                footerComment = 2
        }

        # We have a non-blank line after blank lines. Handle this.
        (blankLines > 0) {
                print lines
                for (i = 0; i < blankLines; i++) {
                        print ""
                }

                lines = ""
                blankLines = 0
                isFooter = 1
                footerComment = 0
        }

        # Detect that the current block is not the footer
        (footerComment == 0) && (!/^\[?[a-zA-Z0-9-]+:/ || /^[a-zA-Z0-9-]+:\/\//) {
                isFooter = 0
        }

        {
                # We need this information about the current last comment line
                if (footerComment == 2) {
                        footerComment = 0
                }
                if (lines != "") {
                        lines = lines "\n";
                }
                lines = lines $0
        }

        # Footer handling:
        # If the last block is considered a footer, splice in the Change-Id at the
        # right place.
        # Look for the right place to inject Change-Id by considering
        # CHANGE_ID_AFTER. Keys listed in it (case insensitive) come first,
        # then Change-Id, then everything else (eg. Signed-off-by:).
        #
        # Otherwise just print the last block, a new line and the Change-Id as a
        # block of its own.
        END {
                unprinted = 1
                if (isFooter == 0) {
                        print lines "\n"
                        lines = ""
                }
                changeIdAfter = "^(" tolower("'"$CHANGE_ID_AFTER"'") "):"
                numlines = split(lines, footer, "\n")
                for (line = 1; line <= numlines; line++) {
                        if (unprinted && match(tolower(footer[line]), changeIdAfter) != 1) {
                                unprinted = 0
                                print "Change-Id: I'"$id"'"
                        }
                        print footer[line]
                }
                if (unprinted) {
                        print "Change-Id: I'"$id"'"
                }
        }' "$MSG" > "$T" && mv "$T" "$MSG" || rm -f "$T"
}
_gen_ChangeIdInput() {
        echo "tree `git write-tree`"
        if parent=`git rev-parse "HEAD^0" 2>/dev/null`
        then
                echo "parent $parent"
        fi
        echo "author `git var GIT_AUTHOR_IDENT`"
        echo "committer `git var GIT_COMMITTER_IDENT`"
        echo
        printf '%s' "$clean_message"
}
_gen_ChangeId() {
        _gen_ChangeIdInput |
        git hash-object -t commit --stdin
}

add_ChangeId


# ================ 校验逻辑 ================
MSG="$1"

# 添加 Signed-off-by 并确保在 Change-Id 之前
add_sign_off() {
    AUTHOR_NAME=$(git var GIT_AUTHOR_IDENT | sed -n 's/^\(.*\) <.*$/\1/p')
    AUTHOR_EMAIL=$(git var GIT_AUTHOR_IDENT | sed -n 's/^.* <\(.*\)> .*$/\1/p')
    SIGNED_OFF_LINE="Signed-off-by: $AUTHOR_NAME <$AUTHOR_EMAIL>"
    
    if grep -q "^Signed-off-by:" "$MSG"; then
        return
    fi
    
    # 如果有 Change-Id，则在它之前插入 Signed-off-by
    if grep -q "^Change-Id:" "$MSG"; then
        sed -i "/^Change-Id:/i $SIGNED_OFF_LINE" "$MSG"
    else
        echo "$SIGNED_OFF_LINE" >> "$MSG"
    fi
}

display_width() {
  str="$1"
  # 统计总字符数（非字节数）
  char_count=$(printf "%s" "$str" | wc -m)
  # 统计全角字符数量（覆盖常见CJK字符）
  fullwidth_count=$(printf "%s" "$str" | grep -oP '[\x{3000}-\x{303F}\x{4E00}-\x{9FFF}\x{FF00}-\x{FFEF}]' | wc -l)
  echo $((char_count + fullwidth_count))
}

# 检查字符串是否包含中文字符
contains_chinese() {
  str="$1"
  # 检查是否包含中文字符（CJK统一汉字、中文标点符号、全角字符）
  if printf "%s" "$str" | grep -qP '[\x{3000}-\x{303F}\x{4E00}-\x{9FFF}\x{FF00}-\x{FFEF}]'; then
    return 0  # 包含中文字符
  else
    return 1  # 不包含中文字符
  fi
}

# 智能换行函数
auto_wrap_line() {
    local text="$1"
    local max_width="$2"
    local result=""
    local current_line=""
    local words

    # 将文本按空格分割成单词数组
    IFS=' ' read -ra words <<< "$text"

    for word in "${words[@]}"; do
        # 计算添加当前单词后的行宽度
        if [ -z "$current_line" ]; then
            test_line="$word"
        else
            test_line="$current_line $word"
        fi

        # 如果添加当前单词后超过限制
        if [ $(display_width "$test_line") -gt $max_width ]; then
            # 如果当前行不为空，先输出当前行
            if [ -n "$current_line" ]; then
                if [ -n "$result" ]; then
                    result="$result\n$current_line"
                else
                    result="$current_line"
                fi
                current_line="$word"
            else
                # 单个单词就超过限制，强制换行
                if [ -n "$result" ]; then
                    result="$result\n$word"
                else
                    result="$word"
                fi
                current_line=""
            fi
        else
            current_line="$test_line"
        fi
    done

    # 添加最后一行
    if [ -n "$current_line" ]; then
        if [ -n "$result" ]; then
            result="$result\n$current_line"
        else
            result="$current_line"
        fi
    fi

    echo -e "$result"
}

# 自动修复超长行
auto_fix_long_lines() {
    local msg_file="$1"
    local fixed=false
    local temp_file=$(mktemp)

    # 备份原文件
    cp "$msg_file" "$msg_file.before_autowrap"

    # 逐行处理
    while IFS= read -r line; do
        # 跳过注释行和空行
        if [[ "$line" =~ ^#.*$ ]] || [ -z "$line" ]; then
            echo "$line" >> "$temp_file"
            continue
        fi

        # 检查行长度
        line_width=$(display_width "$line")

        # 判断是否为Head第一行（包含模块标签）
        if echo "$line" | grep -qE '^\[[^]]+\]'; then
            max_width=65
        else
            max_width=70
        fi

        # 如果超过限制，进行自动换行
        if [ $line_width -gt $max_width ]; then
            echo "# [自动换行] 原行长度: $line_width 字符，已自动换行" >> "$temp_file"
            auto_wrap_line "$line" $max_width >> "$temp_file"
            fixed=true
        else
            echo "$line" >> "$temp_file"
        fi
    done < "$msg_file"

    # 如果有修改，替换原文件并提示用户
    if [ "$fixed" = true ]; then
        mv "$temp_file" "$msg_file"
        echo ""
        echo "=================================================="
        echo "[自动换行] 检测到超长行，已自动进行换行处理"
        echo "原始文件已备份为: $msg_file.before_autowrap"
        echo "请检查换行结果是否符合预期，如需要可手动调整"
        echo "=================================================="
        echo ""
        return 0
    else
        rm "$temp_file"
        return 1
    fi
}

# 提交信息格式校验
validate_commit_format() {
    # 备份原始内容
    cp "$MSG" "$MSG.bak"

    # 首先尝试自动修复超长行
    has_long_lines=false
    if auto_fix_long_lines "$MSG"; then
        has_long_lines=true
        echo "[INFO] 已自动修复超长行，继续进行格式校验..."
    fi

    # 创建临时文件（跳过注释行）
    tmp_file=$(mktemp)
    sed -e '/^#/d' "$MSG" > "$tmp_file"

    # 1. 读取Head部分（最多3行，遇到空行停止）
    head_lines=()
    while IFS= read -r line && [ ${#head_lines[@]} -lt 3 ]; do
        if [ -z "$line" ]; then
            break
        fi
        head_lines+=("$line")
    done < "$tmp_file"

    # 检查Head行数
    if [ ${#head_lines[@]} -lt 1 ]; then
        echo "[ERROR] Head不能为空"
        rm "$tmp_file"
        exit 1
    fi

    # 1.1 检查第一行格式
    if ! echo "${head_lines[0]}" | grep -qE '^(\[[^]]+\])+ .*$'; then
        echo "[ERROR] Head第一行格式错误! 必须为[模块名]+空格开头。"
        echo "Head内容: ${head_lines[0]}"
        rm "$tmp_file"
        exit 1
    fi

    # 1.2 检查第一行长度
    if [ $(display_width "${head_lines[0]}") -gt 65 ]; then
        echo "[ERROR] Head第一行长度仍然超过65字符限制，字符数：$(display_width "${head_lines[0]}")"
        echo "[提示] 自动换行可能无法完美处理模块标签行，请手动调整"
        echo "标题内容: ${head_lines[0]}"
        rm "$tmp_file"
        exit 1
    fi

    # 1.3 检查第一行是否包含中文字符
    if contains_chinese "${head_lines[0]}"; then
        echo "[ERROR] 标题首行不能包含中文字符"
        echo "标题内容: ${head_lines[0]}"
        rm "$tmp_file"
        exit 1
    fi

    # 1.4 检查第二、三行模块名
    for i in 1 2; do
        if [ $i -lt ${#head_lines[@]} ]; then
            if echo "${head_lines[$i]}" | grep -qE '^\[[^]]+\]'; then
                echo "[ERROR] Head第$((i+1))行格式错误: 模块名只能出现在第一行"
                echo "行内容: ${head_lines[$i]}"
                rm "$tmp_file"
                exit 1
            fi
        fi
    done

    # 2. 检查Head与Body之间的空行
    total_lines=$(wc -l < "$tmp_file")
    if [ $total_lines -gt ${#head_lines[@]} ]; then
        # 检查Head后第一行是否为空行
        after_head_line=$(sed -n "$((${#head_lines[@]} + 1))p" "$tmp_file")
        if [ -n "$after_head_line" ]; then
            echo "[ERROR] Head与Body之间必须有空行"
            rm "$tmp_file"
            exit 1
        fi
    else
        echo "[ERROR] Head与Body之间必须有空行"
        rm "$tmp_file"
        exit 1
    fi

    # 清理临时文件
    rm "$tmp_file"

    # 3. 校验Body行长度（跳过注释和空行）
    CONTENT=$(sed -e '/^#/d' -e '/^$/d' "$MSG")
    body_start=$(( ${#head_lines[@]} + 1 ))
    while IFS= read -r line; do
        if [ $(display_width "$line") -gt 70 ]; then
            echo "[ERROR] Body行仍然超过70字符限制，字符数：$(display_width "$line")"
            echo "[提示] 自动换行已尝试处理，但此行可能需要手动调整"
            echo "行内容: $line"
            exit 1
        fi
    done < <(echo "$CONTENT" | tail -n +$body_start)

    # 4. 检查正文至少包含一行不包含中文字符的行
    has_non_chinese_line=false
    while IFS= read -r line; do
        # 跳过Change-Id行
        if echo "$line" | grep -q '^Change-Id:'; then
            continue
        fi
        # 跳过Signed-off-by行
        if echo "$line" | grep -q '^Signed-off-by:'; then
            continue
        fi
        # 跳过空行
        if [ -n "$line" ]; then
            if ! contains_chinese "$line"; then
                has_non_chinese_line=true
                break
            fi
        fi
    done < <(echo "$CONTENT" | tail -n +$body_start)

    if [ "$has_non_chinese_line" = false ]; then
        echo "[ERROR] 正文至少需要包含一行不包含中文字符的行"
        exit 1
    fi

    # 5. 自动格式化修正
    sed -i -E '
        s/^(\[[^]]+\]\s+)[Ff]ix(ed)?\b/\1Fix/i
        # 统一规范冒号后空格
        s/:\s*/: /
        s/：\s*/: /
    ' "$MSG"

    # 6. 标签完整性校验
    REQUIRED_TAGS=()
    # 6.1 标题检测到字符串Bug/bug时，标题拦截没有”Bug “+包含5位（或更多位）ID或没有[bug]标签的提交
    if echo "${head_lines[0]}" | grep -q 'Bug' || echo "${head_lines[0]}" | grep -q 'bug'; then
        if ! echo "${head_lines[0]}" | grep -q 'Bug [0-9]\{5,\}'; then
            echo "[ERROR] Bug后必须跟上ID，例如Bug 12345"
            exit 1
        fi
        if ! echo "${head_lines[0]}" | grep -q '\[bug\]'; then
            echo "[ERROR] Bug提交必须包含[bug]标签"
            exit 1
        fi
        REQUIRED_TAGS=("Problem Description" "Root Cause" "Solution" "Test Scope")
    else
        REQUIRED_TAGS=("Test Scope")
    fi
    
    for tag in "${REQUIRED_TAGS[@]}"; do
        if ! grep -q "\[$tag\]" "$MSG"; then
            echo "[ERROR] 缺失必要标签: [$tag]"
            exit 1
        fi
    done

    if [ "$has_long_lines" = true ]; then
        echo "[ERROR] 请检查自动换行结果并重新提交"
        exit 1
    fi

    mv "$MSG.bak" "$MSG.bak.old"
}

get_git_dir() {
    local git_dir=$(git rev-parse --absolute-git-dir 2>/dev/null)
    if [ -z "$git_dir" ]; then
        echo "无法读取git路径"
        exit 1
    fi
    echo "$git_dir"
}

# 获取当前仓库的git目录
git_dir=$(get_git_dir)
# 检测cherry-pick状态
cherry_pick_file="$git_dir"/CHERRY_PICK_HEAD
if [ -f "$cherry_pick_file" ]; then
    echo "当前处于cherry-pick状态，跳过commit-msg校验"
    exit 0
fi


# 执行校验流程
add_sign_off
validate_commit_format
